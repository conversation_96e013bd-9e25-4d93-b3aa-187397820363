<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

$config = new Config;

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_save'])) {
	try {
		$config->trm               = limpiar_datos($_POST['trm']);
		$config->trk_id            = limpiar_datos($_POST['trk_id']);
		$config->trk_meta_diaria   = limpiar_datos($_POST['trk_meta_diaria']);
		$config->trk_meta_semanal  = limpiar_datos($_POST['trk_meta_semanal']);
		$config->trk_meta_mensual  = limpiar_datos($_POST['trk_meta_mensual']);

		validar_textovacio($config->trm, 'Specify trm.');

		$config->save($conexion);
		
		$success_display = 'show';
		$success_text    = "Save completed";
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}

try {
	$config = Config::get_all_configs_page($conexion);
	
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}

require_once __ROOT__ . '/views/configs.view.php';

?>